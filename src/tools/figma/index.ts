import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
// TODO: 这些工具还需要实现
// import { getFigmaDataTool } from "./get_figma_data/index.js";
// import { downloadFigmaImagesTool } from "./download_figma_images/index.js";

// 定义工具类型
interface ToolConfig {
  name: string;
  config: {
    title: string;
    description: string;
    inputSchema?: any;
  };
  handler: (args: any) => Promise<any>;
}

// Figma 工具列表
const figmaTools: ToolConfig[] = [
  // TODO: 添加 Figma 工具
  // getFigmaDataTool,
  // downloadFigmaImagesTool
];

/**
 * 注册所有 Figma 工具到 MCP 服务器
 */
export function registerFigmaTools(server: McpServer) {
  figmaTools.forEach(tool => {
    server.registerTool(
      tool.name,
      tool.config,
      tool.handler
    );
  });
}

/**
 * 获取所有 Figma 工具的信息
 */
export function getFigmaToolsInfo() {
  return figmaTools.map(tool => ({
    name: tool.name,
    title: tool.config.title,
    description: tool.config.description
  }));
}

export { figmaTools };
