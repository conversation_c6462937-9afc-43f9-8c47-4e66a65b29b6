// Figma API 相关类型定义
export type FigmaAuthOptions = {
  figmaApiKey: string;
  figmaOAuthToken: string;
  useOAuth: boolean;
};

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export type CSSRGBAColor = `rgba(${number}, ${number}, ${number}, ${number})`;
export type CSSHexColor = `#${string}`;

export interface ColorValue {
  hex: string;
  opacity: number;
}

export type SimplifiedFill =
  | {
      type?: string;
      hex?: string;
      rgba?: string;
      opacity?: number;
      imageRef?: string;
      scaleMode?: string;
      gradientHandlePositions?: any[];
      gradientStops?: {
        position: number;
        color: ColorValue | string;
      }[];
    }
  | CSSRGBAColor
  | CSSHexColor;

export interface ComponentProperties {
  name: string;
  value: string;
  type: string;
}

export interface SimplifiedNode {
  id: string;
  name: string;
  type: string; // e.g. FRAME, TEXT, INSTANCE, RECTANGLE, etc.
  // geometry
  boundingBox?: BoundingBox;
  // text
  text?: string;
  textStyle?: string;
  // appearance
  fills?: string;
  styles?: string;
  strokes?: string;
  effects?: string;
  opacity?: number;
  borderRadius?: string;
  // layout & alignment
  layout?: string;
  componentId?: string;
  componentProperties?: ComponentProperties[];
  // children
  children?: SimplifiedNode[];
}

export interface SimplifiedDesign {
  name: string;
  lastModified: string;
  thumbnailUrl: string;
  nodes: SimplifiedNode[];
  components: Record<string, any>;
  componentSets: Record<string, any>;
  globalVars: {
    styles: Record<string, any>;
  };
}

export type FetchImageParams = {
  nodeId: string;
  fileName: string;
  fileType: "png" | "svg";
};

export type FetchImageFillParams = Omit<FetchImageParams, "fileType"> & {
  imageRef: string;
};

export interface SvgOptions {
  outlineText: boolean;
  includeId: boolean;
  simplifyStroke: boolean;
}
