{"name": "mcp-shell-executor", "version": "1.0.0", "description": "MCP server for executing local shell commands", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx --watch src/index.ts", "dev:inspector": "tsx --watch src/index.ts & sleep 2 && npx @modelcontextprotocol/inspector tsx src/index.ts", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "node --test"}, "keywords": ["mcp", "shell", "executor", "modelcontextprotocol"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.0", "@types/js-yaml": "^4.0.9", "js-yaml": "^4.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.3.0"}}