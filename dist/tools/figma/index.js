// Figma 工具列表
const figmaTools = [
// TODO: 添加 Figma 工具
// getFigmaDataTool,
// downloadFigmaImagesTool
];
/**
 * 注册所有 Figma 工具到 MCP 服务器
 */
export function registerFigmaTools(server) {
    figmaTools.forEach(tool => {
        server.registerTool(tool.name, tool.config, tool.handler);
    });
}
/**
 * 获取所有 Figma 工具的信息
 */
export function getFigmaToolsInfo() {
    return figmaTools.map(tool => ({
        name: tool.name,
        title: tool.config.title,
        description: tool.config.description
    }));
}
export { figmaTools };
//# sourceMappingURL=index.js.map